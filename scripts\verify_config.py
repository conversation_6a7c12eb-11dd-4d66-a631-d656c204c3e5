#!/usr/bin/env python3
"""
配置验证脚本 - 验证对话体验优化配置是否正确应用

使用方法:
python scripts/verify_config.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import settings
from loguru import logger

def verify_interruption_config():
    """验证打断逻辑配置"""
    print("🔍 验证打断逻辑配置...")
    
    expected_values = {
        "cooperative_interruption_threshold": 0.3,
        "disruptive_interruption_threshold": 0.4,
        "silence_duration_ms": 800,
        "response_delay_ms": 200
    }
    
    for key, expected in expected_values.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {key}: {actual} (期望: {expected})")

def verify_vad_config():
    """验证VAD配置"""
    print("\n🔍 验证VAD配置...")
    
    # STANDARD模式
    print("  STANDARD模式:")
    standard_expected = {
        "vad_standard_threshold": 0.4,
        "vad_standard_prefix_padding_ms": 400,
        "vad_standard_silence_duration_ms": 800
    }
    
    for key, expected in standard_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"    {status} {key}: {actual} (期望: {expected})")
    
    # BARGE_IN模式
    print("  BARGE_IN模式:")
    barge_in_expected = {
        "vad_barge_in_threshold": 0.2,
        "vad_barge_in_prefix_padding_ms": 150,
        "vad_barge_in_silence_duration_ms": 600
    }
    
    for key, expected in barge_in_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"    {status} {key}: {actual} (期望: {expected})")

def verify_buffer_config():
    """验证缓冲区配置"""
    print("\n🔍 验证缓冲区配置...")
    
    buffer_expected = {
        "tactical_buffer_min_length": 3,
        "tactical_buffer_max_length": 15,
        "strategic_buffer_min_length": 5,
        "strategic_buffer_max_length": 25
    }
    
    for key, expected in buffer_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {key}: {actual} (期望: {expected})")

def verify_timeout_config():
    """验证超时配置"""
    print("\n🔍 验证超时配置...")
    
    timeout_expected = {
        "strategic_analysis_timeout": 3000,
        "tactical_analysis_timeout": 2000
    }
    
    for key, expected in timeout_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {key}: {actual} (期望: {expected})")

def verify_transcription_config():
    """验证转录配置"""
    print("\n🔍 验证转录配置...")
    
    transcription_expected = {
        "paraformer_confidence_threshold": 0.7
    }
    
    for key, expected in transcription_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {key}: {actual} (期望: {expected})")

def verify_interviewer_config():
    """验证面试官模式配置"""
    print("\n🔍 验证面试官模式配置...")
    
    interviewer_expected = {
        "interviewer_cooperative_threshold": 0.4,
        "interviewer_disruptive_threshold": 0.3,
        "interviewer_max_speaking_time": 20,
        "interviewer_force_interrupt_after": 40
    }
    
    for key, expected in interviewer_expected.items():
        actual = getattr(settings, key)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {key}: {actual} (期望: {expected})")

def verify_new_config():
    """验证新增配置"""
    print("\n🔍 验证新增配置...")
    
    # 检查新增字段是否存在
    new_fields = [
        "min_response_interval_ms",
        "max_ai_speaking_duration_sec"
    ]
    
    for field in new_fields:
        if hasattr(settings, field):
            value = getattr(settings, field)
            print(f"  ✅ {field}: {value}")
        else:
            print(f"  ❌ {field}: 字段不存在")

def main():
    """主验证函数"""
    print("🚀 Audio Agent 对话体验优化配置验证")
    print("=" * 50)
    
    try:
        verify_interruption_config()
        verify_vad_config()
        verify_buffer_config()
        verify_timeout_config()
        verify_transcription_config()
        verify_interviewer_config()
        verify_new_config()
        
        print("\n" + "=" * 50)
        print("✅ 配置验证完成！")
        print("\n💡 如果发现配置不匹配，请检查:")
        print("   1. .env 文件是否正确更新")
        print("   2. 是否需要重启服务以应用新配置")
        print("   3. 环境变量是否被其他配置覆盖")
        
    except Exception as e:
        print(f"\n❌ 配置验证失败: {e}")
        print("请检查配置文件是否存在语法错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
